export default function AboutMe() {
  return (
    <section className="relative py-20 overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0" style={{ backgroundColor: '#16165A' }}>
        {/* Decorative elements */}
        <div className="absolute top-20 left-20 w-40 h-40 rounded-full opacity-5" style={{ backgroundColor: '#5FE785' }}></div>
        <div className="absolute bottom-20 right-20 w-32 h-32 rounded-full opacity-5" style={{ backgroundColor: '#05044a' }}></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

        {/* Main Title */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
            About Me
          </h2>
        </div>

        {/* About Me Content */}
        <div className="grid lg:grid-cols-2 gap-12 items-center mb-20">
          {/* Left content - Profile Image */}
          <div className="flex justify-center lg:justify-end">
            <div className="relative">
              <div className="w-80 h-96 rounded-2xl overflow-hidden border-4 shadow-2xl" style={{ borderColor: '#5FE785' }}>
                <img
                  src="/ink.JPG"
                  alt="Kiruthika Kulandaivel - Founder of StockInk"
                  className="w-full h-full object-cover"
                />
              </div>
              {/* Decorative border */}
              <div className="absolute -inset-4 rounded-2xl border-2 opacity-30" style={{ borderColor: '#5FE785' }}></div>
            </div>
          </div>

          {/* Right content - About Information */}
          <div className="lg:pl-8">
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
              <h3 className="text-3xl md:text-4xl font-bold text-white mb-2">
                Kiruthika Kulandaivel
              </h3>
              <div className="mb-6">
                <span className="text-xl font-semibold" style={{ color: '#5FE785' }}>
                  Founder – InkCharts
                </span>
              </div>

              <div className="space-y-6">
                {/* Experience */}
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 rounded-full flex items-center justify-center flex-shrink-0" style={{ backgroundColor: '#5FE785' }}>
                    <svg className="w-6 h-6" style={{ color: '#05044a' }} fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-xl font-bold text-white mb-2">Experience</h4>
                    <p className="text-gray-200 leading-relaxed">
                      6+ years of experience in Indian & forex markets
                    </p>
                  </div>
                </div>

                {/* Students Mentored */}
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 rounded-full flex items-center justify-center flex-shrink-0" style={{ backgroundColor: '#5FE785' }}>
                    <svg className="w-6 h-6" style={{ color: '#05044a' }} fill="currentColor" viewBox="0 0 20 20">
                      <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-xl font-bold text-white mb-2">Students Mentored</h4>
                    <p className="text-gray-200 leading-relaxed">
                      Educated and mentored over <span className="font-bold" style={{ color: '#5FE785' }}>2000+ learners</span>
                    </p>
                  </div>
                </div>

                {/* Specialization */}
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 rounded-full flex items-center justify-center flex-shrink-0" style={{ backgroundColor: '#5FE785' }}>
                    <svg className="w-6 h-6" style={{ color: '#05044a' }} fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-xl font-bold text-white mb-2">Specialization</h4>
                    <p className="text-gray-200 leading-relaxed">
                      Specializes in trading psychology and risk management
                    </p>
                  </div>
                </div>

                {/* Passion */}
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 rounded-full flex items-center justify-center flex-shrink-0" style={{ backgroundColor: '#5FE785' }}>
                    <svg className="w-6 h-6" style={{ color: '#05044a' }} fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-xl font-bold text-white mb-2">Passionate</h4>
                    <p className="text-gray-200 leading-relaxed">
                      Dedicated to empowering traders with knowledge and confidence
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Achievement Stats */}
        <div className="grid md:grid-cols-3 gap-8 mb-20">
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 text-center">
            <div className="text-4xl font-bold mb-4" style={{ color: '#5FE785' }}>6+</div>
            <div className="text-xl font-semibold text-white mb-2">Years</div>
            <div className="text-gray-200">Trading Experience</div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 text-center">
            <div className="text-4xl font-bold mb-4" style={{ color: '#5FE785' }}>2000+</div>
            <div className="text-xl font-semibold text-white mb-2">Students</div>
            <div className="text-gray-200">Successfully Mentored</div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 text-center">
            <div className="text-4xl font-bold mb-4" style={{ color: '#5FE785' }}>Expert</div>
            <div className="text-xl font-semibold text-white mb-2">Level</div>
            <div className="text-gray-200">Trading Psychology</div>
          </div>
        </div>

        {/* Mission Statement */}
        <div className="text-center">
          <div className="bg-white/10 backdrop-blur-sm rounded-3xl p-8 md:p-12 border border-white/20 max-w-4xl mx-auto">
            <h4 className="text-2xl md:text-3xl font-bold mb-6 text-white">
              Mission
            </h4>
            <p className="text-xl text-gray-200 mb-6 leading-relaxed">
              To make gold trading accessible, understandable, and profitable for every trader through
              comprehensive education, practical strategies, and strong psychological foundations.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                className="px-8 py-4 rounded-full font-bold text-lg transition-all duration-300 hover:scale-105 hover:shadow-2xl"
                style={{
                  backgroundColor: '#5FE785',
                  color: '#05044a'
                }}
              >
                Learn from Kiruthika
              </button>
              <button className="px-8 py-4 rounded-full font-bold text-lg border-2 border-white text-white hover:bg-white hover:text-gray-900 transition-all duration-300">
                View Success Stories
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
