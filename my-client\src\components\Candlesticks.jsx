export default function Candlesticks() {
  return (
    <section className="relative py-20 overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0" style={{ backgroundColor: '#16165A' }}>
        {/* Decorative elements */}
        <div className="absolute top-10 left-10 w-36 h-36 rounded-full opacity-5" style={{ backgroundColor: '#5FE785' }}></div>
        <div className="absolute bottom-10 right-10 w-28 h-28 rounded-full opacity-5" style={{ backgroundColor: '#05044a' }}></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Main Title */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
            CANDLE STICKS (STRUCTURE)
          </h2>
        </div>

        {/* Candlestick Introduction with Image */}
        <div className="grid lg:grid-cols-2 gap-12 items-center mb-20">
          {/* Left content - Candle Image */}
          <div className="flex justify-center">
            <div className="relative">
              <img 
                src="/candle.png" 
                alt="Candlestick Structure" 
                className="rounded-2xl shadow-2xl max-w-full h-auto border-2"
                style={{ borderColor: '#5FE785' }}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent rounded-2xl"></div>
            </div>
          </div>

          {/* Right content - Introduction */}
          <div>
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
              <h3 className="text-3xl font-bold mb-6 text-white">
                Understanding Candlestick Patterns
              </h3>
              <p className="text-lg text-gray-200 leading-relaxed">
                Candlesticks are the foundation of technical analysis. Each candle tells a story of price movement, showing the open, high, low, and close prices for a specific time period.
              </p>
            </div>
          </div>
        </div>

        {/* Swing High and Swing Low Title */}
        <div className="text-center mb-16">
          <h3 className="text-3xl md:text-4xl font-bold text-white mb-4">
            SWING HIGH AND SWING LOW
          </h3>
        </div>

        {/* Swing High Section */}
        <div className="mb-20">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left content - Swing High explanation */}
            <div>
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
                <h4 className="text-2xl font-bold mb-6" style={{ color: '#5FE785' }}>
                  SWING HIGH
                </h4>
                
                <p className="text-lg text-gray-200 mb-6 leading-relaxed">
                  Swing high identify panna oru simple technique irukku – Group of 3 candle logic. Ithu romba powerful-a work aagum.
                </p>
                
                <div className="bg-white/5 rounded-xl p-6 mb-6">
                  <p className="text-lg font-semibold mb-4" style={{ color: '#5FE785' }}>
                    Listen carefully 👇:
                  </p>
                  
                  <div className="space-y-4">
                    <div className="flex items-start space-x-4">
                      <div className="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 mt-1" style={{ backgroundColor: '#5FE785' }}>
                        <span className="text-sm font-bold" style={{ color: '#05044a' }}>1</span>
                      </div>
                      <p className="text-gray-200">Imagine 3 candles side-by-side.</p>
                    </div>
                    
                    <div className="flex items-start space-x-4">
                      <div className="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 mt-1" style={{ backgroundColor: '#5FE785' }}>
                        <span className="text-sm font-bold" style={{ color: '#05044a' }}>2</span>
                      </div>
                      <p className="text-gray-200">The middle candle is called the <span className="font-bold text-white">Mind Candle</span>.</p>
                    </div>
                    
                    <div className="flex items-start space-x-4">
                      <div className="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 mt-1" style={{ backgroundColor: '#5FE785' }}>
                        <span className="text-sm font-bold" style={{ color: '#05044a' }}>3</span>
                      </div>
                      <div>
                        <p className="text-gray-200 mb-2">If that Mind Candle's high is higher than:</p>
                        <div className="ml-4 space-y-2">
                          <p className="text-gray-300">○ The candle before it (previous candle),</p>
                          <p className="text-gray-300">○ And the candle after it (next candle),</p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-start space-x-4">
                      <div className="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 mt-1" style={{ backgroundColor: '#5FE785' }}>
                        <span className="text-sm font-bold" style={{ color: '#05044a' }}>4</span>
                      </div>
                      <p className="text-gray-200">Then it's a <span className="font-bold text-white">Swing High</span></p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right content - Swing High Image */}
            <div className="flex justify-center">
              <div className="relative">
                <img 
                  src="/swing_high.png" 
                  alt="Swing High Pattern" 
                  className="rounded-2xl shadow-2xl max-w-full h-auto border-2"
                  style={{ borderColor: '#5FE785' }}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent rounded-2xl"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Swing Low Section */}
        <div className="mb-20">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left content - Swing Low Image */}
            <div className="flex justify-center lg:order-1">
              <div className="relative">
                <img 
                  src="/swing_low.png" 
                  alt="Swing Low Pattern" 
                  className="rounded-2xl shadow-2xl max-w-full h-auto border-2"
                  style={{ borderColor: '#5FE785' }}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent rounded-2xl"></div>
              </div>
            </div>

            {/* Right content - Swing Low explanation */}
            <div className="lg:order-2">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
                <h4 className="text-2xl font-bold mb-6" style={{ color: '#5FE785' }}>
                  SWING LOW
                </h4>
                
                <p className="text-lg text-gray-200 mb-6 leading-relaxed">
                  Swing low-a identify panna, use the Group of 3 Candle Logic. Very simple and powerful.
                </p>
                
                <div className="bg-white/5 rounded-xl p-6 mb-6">
                  <p className="text-lg font-semibold mb-4" style={{ color: '#5FE785' }}>
                    Listen carefully 👇:
                  </p>
                  
                  <div className="space-y-4">
                    <div className="flex items-start space-x-4">
                      <div className="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 mt-1" style={{ backgroundColor: '#5FE785' }}>
                        <span className="text-sm font-bold" style={{ color: '#05044a' }}>1</span>
                      </div>
                      <p className="text-gray-200">Think of 3 candles in a row.</p>
                    </div>
                    
                    <div className="flex items-start space-x-4">
                      <div className="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 mt-1" style={{ backgroundColor: '#5FE785' }}>
                        <span className="text-sm font-bold" style={{ color: '#05044a' }}>2</span>
                      </div>
                      <p className="text-gray-200">The middle candle is your <span className="font-bold text-white">Mind Candle</span>.</p>
                    </div>
                    
                    <div className="flex items-start space-x-4">
                      <div className="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 mt-1" style={{ backgroundColor: '#5FE785' }}>
                        <span className="text-sm font-bold" style={{ color: '#05044a' }}>3</span>
                      </div>
                      <div>
                        <p className="text-gray-200 mb-2">If that Mind Candle's low is lower than:</p>
                        <div className="ml-4 space-y-2">
                          <p className="text-gray-300">○ The candle before it (previous candle),</p>
                          <p className="text-gray-300">○ And the candle after it (next candle),</p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-start space-x-4">
                      <div className="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 mt-1" style={{ backgroundColor: '#5FE785' }}>
                        <span className="text-sm font-bold" style={{ color: '#05044a' }}>4</span>
                      </div>
                      <p className="text-gray-200">Then it's a valid <span className="font-bold text-white">Swing Low!</span></p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Summary Section */}
        <div className="text-center">
          <div className="bg-white/10 backdrop-blur-sm rounded-3xl p-8 md:p-12 border border-white/20">
            <h4 className="text-2xl font-bold mb-6 text-white">
              Master the 3-Candle Logic
            </h4>
            <p className="text-lg text-gray-200 mb-8 max-w-4xl mx-auto leading-relaxed">
              Understanding swing highs and swing lows is fundamental to reading market structure. This simple yet powerful technique will help you identify key turning points in the market.
            </p>
            <button 
              className="px-8 py-4 rounded-full font-bold text-lg transition-all duration-300 hover:scale-105 hover:shadow-2xl"
              style={{ 
                backgroundColor: '#5FE785', 
                color: '#05044a' 
              }}
            >
              Practice Identifying Swings
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
