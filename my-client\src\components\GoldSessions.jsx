export default function GoldSessions() {
  return (
    <section className="relative py-20 overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0" style={{ backgroundColor: '#16165A' }}>
        {/* Decorative elements */}
        <div className="absolute top-10 left-10 w-44 h-44 rounded-full opacity-5" style={{ backgroundColor: '#5FE785' }}></div>
        <div className="absolute bottom-10 right-10 w-36 h-36 rounded-full opacity-5" style={{ backgroundColor: '#05044a' }}></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

        {/* Main Title */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
            Gold Session Volatility Breakdown
          </h2>
          <div className="backdrop-blur-sm rounded-2xl p-8 border border-white/20 max-w-4xl mx-auto" style={{ backgroundColor: 'rgb(5, 4, 74)' }}>
            <p className="text-xl text-gray-200 mb-4 leading-relaxed">
              "Gold-la candles paathu trade panradhu dhaan illa.
            </p>
            <p className="text-xl text-gray-200 mb-4 leading-relaxed">
              Neenga trade panradhu – London and New York boardroom decisions.
            </p>
            <p className="text-xl font-semibold text-white">
              So, session-wise volatility-a purinjukonga… adhu than ultimate edge!"
            </p>
          </div>
        </div>

        {/* Trading Sessions */}
        <div className="mb-20">
          <div className="grid lg:grid-cols-3 gap-8">

            {/* Asian Session */}
            <div className="backdrop-blur-sm rounded-2xl p-8 border border-white/20 hover:border-green-400 transition-all duration-300" style={{ backgroundColor: 'rgb(5, 4, 74)' }}>
              <div className="text-center mb-6">
                <div className="w-20 h-20 rounded-full mx-auto mb-4 flex items-center justify-center" style={{ backgroundColor: '#5FE785' }}>
                  <span className="text-lg font-bold" style={{ color: '#05044a' }}>ASIA</span>
                </div>
                <h3 className="text-2xl font-bold text-white mb-2">1. Asian Session</h3>
                <p className="text-lg" style={{ color: '#5FE785' }}>(5:30 AM – 10:30 AM IST)</p>
              </div>

              <div className="space-y-4 mb-6">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                  <p className="text-gray-200">Mostly sideways / consolidation</p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                  <p className="text-gray-200">Volume low</p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                  <p className="text-gray-200">Good for chart planning, levels marking</p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                  <p className="text-gray-200">Smart money usually waits here, no big moves</p>
                </div>
              </div>

              <div className="rounded-xl p-4" style={{ backgroundColor: 'rgba(5, 4, 74, 0.5)' }}>
                <p className="text-gray-200 text-sm leading-relaxed">
                  "Asian session la gold calm-ah irukkum, planning pannunga, trade panna rush pannadhinga."
                </p>
              </div>
            </div>

            {/* London Session */}
            <div className="backdrop-blur-sm rounded-2xl p-8 border border-white/20 hover:border-green-400 transition-all duration-300" style={{ backgroundColor: 'rgb(5, 4, 74)' }}>
              <div className="text-center mb-6">
                <div className="w-20 h-20 rounded-full mx-auto mb-4 flex items-center justify-center" style={{ backgroundColor: '#5FE785' }}>
                  <span className="text-lg font-bold" style={{ color: '#05044a' }}>LON</span>
                </div>
                <h3 className="text-2xl font-bold text-white mb-2">2. London Session</h3>
                <p className="text-lg" style={{ color: '#5FE785' }}>(12:30 PM – 5:30 PM IST)</p>
              </div>

              <div className="space-y-4 mb-6">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                  <p className="text-gray-200">Volume pick up aagum</p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                  <p className="text-gray-200">Breakouts, Trendline breaks, BOS (Break of Structure) start here</p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                  <p className="text-gray-200">Big players enter</p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                  <p className="text-gray-200">Best for directional trades</p>
                </div>
              </div>

              <div className="rounded-xl p-4" style={{ backgroundColor: 'rgba(5, 4, 74, 0.5)' }}>
                <p className="text-gray-200 text-sm leading-relaxed mb-2">
                  London open aana udane… market-kulla oru pulse varum.
                </p>
                <p className="text-gray-200 text-sm leading-relaxed">
                  If you trade trendlines, this is your gold mine.
                </p>
              </div>
            </div>

            {/* New York Session */}
            <div className="backdrop-blur-sm rounded-2xl p-8 border border-white/20 hover:border-green-400 transition-all duration-300" style={{ backgroundColor: 'rgb(5, 4, 74)' }}>
              <div className="text-center mb-6">
                <div className="w-20 h-20 rounded-full mx-auto mb-4 flex items-center justify-center" style={{ backgroundColor: '#5FE785' }}>
                  <span className="text-lg font-bold" style={{ color: '#05044a' }}>NYC</span>
                </div>
                <h3 className="text-2xl font-bold text-white mb-2">3. New York Session</h3>
                <p className="text-lg" style={{ color: '#5FE785' }}>(7:00 PM – 11:30 PM IST)</p>
              </div>

              <div className="space-y-4 mb-6">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                  <p className="text-gray-200">Volume max spike here</p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                  <p className="text-gray-200">US data news: CPI, FOMC, NFP → violent moves</p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                  <p className="text-gray-200">Traps and liquidity grabs romba common</p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                  <p className="text-gray-200">Works well with continuation of London bias</p>
                </div>
              </div>

              <div className="bg-white/5 rounded-xl p-4">
                <p className="text-gray-200 text-sm leading-relaxed mb-2">
                  "NY session la vela thani level.
                </p>
                <p className="text-gray-200 text-sm leading-relaxed">
                  News vara 30 mins munnadi – after 30 mins dhaan real move varum."
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* US Economic Data Section */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h3 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Key US Economic Data
            </h3>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">

            {/* CPI */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
              <h4 className="text-2xl font-bold mb-4" style={{ color: '#5FE785' }}>
                1. CPI – Consumer Price Index
              </h4>
              <div className="space-y-4">
                <div>
                  <p className="text-gray-300 text-sm">Full Form:</p>
                  <p className="text-white font-semibold">Consumer Price Index</p>
                </div>
                <div>
                  <p className="text-gray-300 text-sm">Meaning:</p>
                  <p className="text-gray-200">Measures inflation — how much prices of everyday goods and services are increasing.</p>
                </div>
                <div className="bg-white/5 rounded-xl p-4">
                  <p className="text-gray-200 text-sm leading-relaxed">
                    CPI-na namma daily life la pay panra goods rate evalo increase aagudhu-nu kaamikum. Adhan inflation meter."
                  </p>
                </div>
              </div>
            </div>

            {/* FOMC */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
              <h4 className="text-2xl font-bold mb-4" style={{ color: '#5FE785' }}>
                2. FOMC – Federal Open Market Committee
              </h4>
              <div className="space-y-4">
                <div>
                  <p className="text-gray-300 text-sm">Full Form:</p>
                  <p className="text-white font-semibold">Federal Open Market Committee</p>
                </div>
                <div>
                  <p className="text-gray-300 text-sm">Meaning:</p>
                  <p className="text-gray-200">US central bank group that sets interest rates (Federal Reserve policy decisions).</p>
                </div>
                <div className="bg-white/5 rounded-xl p-4">
                  <p className="text-gray-200 text-sm leading-relaxed">
                    "FOMC-na US-la interest rate yaar fix pannuvanga-nu decide panra group. Avanga pesra word la dhan market jump aagum."
                  </p>
                </div>
              </div>
            </div>

            {/* NFP */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
              <h4 className="text-2xl font-bold mb-4" style={{ color: '#5FE785' }}>
                3. NFP – Non-Farm Payrolls
              </h4>
              <div className="space-y-4">
                <div>
                  <p className="text-gray-300 text-sm">Full Form:</p>
                  <p className="text-white font-semibold">Non-Farm Payrolls</p>
                </div>
                <div>
                  <p className="text-gray-300 text-sm">Meaning:</p>
                  <p className="text-gray-200">Monthly US job data report – shows how many jobs were added in non-farming sectors.</p>
                </div>
                <div className="bg-white/5 rounded-xl p-4">
                  <p className="text-gray-200 text-sm leading-relaxed mb-2">
                    "NFP-na US-la evalo vela vaippu increase aachunu kaamikum job report.
                  </p>
                  <p className="text-gray-200 text-sm leading-relaxed">
                    Job count strong-na USD strong, gold weak.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Strategy Tips with Images */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h3 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Strategy Tips
            </h3>
          </div>

          <div className="grid lg:grid-cols-2 gap-12 items-center mb-12">
            {/* Left content */}
            <div>
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
                <h4 className="text-2xl font-bold mb-6" style={{ color: '#5FE785' }}>
                  Avoid session overlap trap
                </h4>
                <div className="text-center mb-6">
                  <div className="text-3xl font-bold mb-2" style={{ color: '#5FE785' }}>🕰 11:30 AM – 1:30 PM IST</div>
                </div>
                <div className="bg-white/5 rounded-xl p-6">
                  <p className="text-gray-200 leading-relaxed">
                    "London open-kum Asian close-kum overlap time la market fake pannum. Breakout trap / fake BOS romba varum."
                  </p>
                </div>
              </div>
            </div>

            {/* Right content - T1 Image */}
            <div className="flex justify-center">
              <div className="relative">
                <img
                  src="/t1.jpeg"
                  alt="Trading Session Example"
                  className="rounded-2xl shadow-2xl max-w-full h-auto border-2"
                  style={{ borderColor: '#5FE785' }}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent rounded-2xl"></div>
              </div>
            </div>
          </div>

          {/* Session Flow Trade Example */}
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left content - T2 Image */}
            <div className="flex justify-center">
              <div className="relative">
                <img
                  src="/t2.jpeg"
                  alt="Session Flow Example"
                  className="rounded-2xl shadow-2xl max-w-full h-auto border-2"
                  style={{ borderColor: '#5FE785' }}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent rounded-2xl"></div>
              </div>
            </div>

            {/* Right content */}
            <div>
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
                <h4 className="text-2xl font-bold mb-6" style={{ color: '#5FE785' }}>
                  Session Flow Trade Example:
                </h4>
                <div className="space-y-4">
                  <div className="flex items-start space-x-4">
                    <div className="w-3 h-3 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                    <p className="text-gray-200">London la uptrend start aana</p>
                  </div>
                  <div className="flex items-start space-x-4">
                    <div className="w-3 h-3 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                    <p className="text-gray-200">NY la small pullback → enter continuation</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Trading vs Gambling Section */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h3 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Trading ≠ Gambling
            </h3>
          </div>

          <div className="grid lg:grid-cols-2 gap-8">
            {/* Gambling */}
            <div className="bg-red-900/20 backdrop-blur-sm rounded-2xl p-8 border border-red-500/30">
              <h4 className="text-2xl font-bold mb-6 text-red-400">
                Gambling
              </h4>
              <div className="space-y-4">
                <div className="bg-red-900/20 rounded-xl p-4">
                  <p className="text-gray-200">No plan</p>
                </div>
                <div className="bg-red-900/20 rounded-xl p-4">
                  <p className="text-gray-200">Blind risk</p>
                </div>
                <div className="bg-red-900/20 rounded-xl p-4">
                  <p className="text-gray-200">Emotion based</p>
                </div>
              </div>
              <div className="mt-6 text-center">
                <p className="text-red-400 font-semibold">gambling-naa hope.</p>
              </div>
            </div>

            {/* Trading */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
              <h4 className="text-2xl font-bold mb-6" style={{ color: '#5FE785' }}>
                Trading
              </h4>
              <div className="space-y-4">
                <div className="bg-white/5 rounded-xl p-4">
                  <p className="text-gray-200">With plan</p>
                </div>
                <div className="bg-white/5 rounded-xl p-4">
                  <p className="text-gray-200">Calculated risk</p>
                </div>
                <div className="bg-white/5 rounded-xl p-4">
                  <p className="text-gray-200">Logic based</p>
                </div>
              </div>
              <div className="mt-6 text-center">
                <p className="text-white font-semibold">Trading-naa strategy + discipline + psychology."</p>
              </div>
            </div>
          </div>
        </div>

        {/* What You Really Trade */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h3 className="text-3xl md:text-4xl font-bold text-white mb-4">
              What You Really Trade in Market:
            </h3>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 text-center">
              <div className="text-2xl font-bold mb-2" style={{ color: '#5FE785' }}>Price</div>
              <p className="text-gray-200">=</p>
              <p className="text-white font-semibold">Numbers</p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 text-center">
              <div className="text-2xl font-bold mb-2" style={{ color: '#5FE785' }}>Candle</div>
              <p className="text-gray-200">=</p>
              <p className="text-white font-semibold">Emotions</p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 text-center">
              <div className="text-2xl font-bold mb-2" style={{ color: '#5FE785' }}>Chart</div>
              <p className="text-gray-200">=</p>
              <p className="text-white font-semibold">Story</p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 text-center">
              <div className="text-2xl font-bold mb-2" style={{ color: '#5FE785' }}>Market</div>
              <p className="text-gray-200">=</p>
              <p className="text-white font-semibold">People's decisions</p>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 text-center">
            <p className="text-lg text-gray-200 mb-4 leading-relaxed">
              Gold oda candle la emotion irukku – fear, greed, panic, hope…
            </p>
            <p className="text-xl font-semibold text-white">
              Trade panradhu price illa – people oda emotion reaction."
            </p>
          </div>
        </div>

        {/* Common Trading Mistakes */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h3 className="text-3xl md:text-4xl font-bold text-white mb-4">
              💣 Common Trading Mistakes:
            </h3>
          </div>

          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
              <h4 className="text-xl font-bold mb-3 text-red-400">1. Overtrading</h4>
              <p className="text-gray-200">(More entries = More loss)</p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
              <h4 className="text-xl font-bold mb-3 text-red-400">2. No stop loss</h4>
              <p className="text-gray-200">(SL illa-na game over)</p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
              <h4 className="text-xl font-bold mb-3 text-red-400">3. FOMO entry</h4>
              <p className="text-gray-200">(Miss pannom nu odane jump panradhu)</p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
              <h4 className="text-xl font-bold mb-3 text-red-400">4. Trading without backtested strategy</h4>
              <p className="text-gray-200"></p>
            </div>
          </div>
        </div>

        {/* Final Impact Line */}
        <div className="text-center">
          <div className="bg-white/10 backdrop-blur-sm rounded-3xl p-8 md:p-12 border border-white/20">
            <h4 className="text-2xl font-bold mb-6" style={{ color: '#5FE785' }}>
              Final Impact Line:
            </h4>
            <p className="text-xl text-gray-200 mb-4 leading-relaxed">
              Trading-nu oru button illa.
            </p>
            <p className="text-2xl font-bold text-white mb-8">
              It's a full-time mental sport – where you win by staying calm, clear, and consistent."
            </p>
            <button
              className="px-8 py-4 rounded-full font-bold text-lg transition-all duration-300 hover:scale-105 hover:shadow-2xl"
              style={{
                backgroundColor: '#5FE785',
                color: '#05044a'
              }}
            >
              Master Session Trading
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
