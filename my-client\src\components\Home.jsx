export default function Home() {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden pt-20">
      {/* Background with consistent color */}
      <div
        className="absolute inset-0"
        style={{ backgroundColor: '#16165A' }}
      >
        {/* Decorative elements */}
        <div className="absolute top-20 left-10 w-32 h-32 rounded-full opacity-10 bg-green-400"></div>
        <div className="absolute bottom-20 right-10 w-48 h-48 rounded-full opacity-10 bg-green-400"></div>
        <div className="absolute top-1/2 left-1/4 w-24 h-24 rounded-full opacity-5 bg-white"></div>
      </div>

      {/* Main content */}
      <div className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        {/* Welcome badge */}
        {/* <div className="inline-flex items-center px-6 py-3 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 mb-8">
          <span
            className="text-lg font-semibold tracking-wide"
            style={{ color: '#5FE785' }}
          >
            WELCOME
          </span>
        </div> */}

        {/* Main heading */}
        <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-8 leading-tight">
          <span className="block">Master</span>
          <span
            className="block mt-2"
            style={{ color: '#5FE785' }}
          >
            Gold Trading
          </span>
          <span className="block text-white">with Confidence</span>
        </h1>

        {/* Introduction */}
        <div className="max-w-4xl mx-auto mb-12">
          <p className="text-xl md:text-2xl text-gray-200 mb-6 leading-relaxed">
            Welcome to <span style={{ color: '#5FE785' }} className="font-bold">InkCharts</span> -
            Your ultimate destination for mastering <span className="font-semibold text-white">XAU/USD trading strategies</span> and
            understanding the <span className="font-semibold text-white">psychology behind profitable gold trading</span>.
          </p>
        </div>

        {/* Key points */}
        <div className="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto mb-12">
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
            <div className="w-16 h-16 rounded-full mx-auto mb-6 flex items-center justify-center" style={{ backgroundColor: '#5FE785' }}>
              <svg className="w-8 h-8" style={{ color: '#05044a' }} fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-2xl font-bold text-white mb-4">Real Trading Experience</h3>
            <p className="text-gray-200 text-lg">
              Intha webinar-la, we're not just gonna learn theory…
              Naama <span className="font-semibold" style={{ color: '#5FE785' }}>real trading mindset, psychology, and live market logic</span> pathi pesaporum.
            </p>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
            <div className="w-16 h-16 rounded-full mx-auto mb-6 flex items-center justify-center" style={{ backgroundColor: '#5FE785' }}>
              <svg className="w-8 h-8" style={{ color: '#05044a' }} fill="currentColor" viewBox="0 0 20 20">
                <path d="M12 2l3.09 6.26L22 9l-5 4.87L18.18 20 12 16.77 5.82 20 7 13.87 2 9l6.91-.74L12 2z" />
              </svg>
            </div>
            <h3 className="text-2xl font-bold text-white mb-4">XAU/USD Focus</h3>
            <p className="text-gray-200 text-lg">
              Especially in <span className="font-bold" style={{ color: '#5FE785' }}>XAU/USD</span>,
              which is one of the most <span className="font-semibold text-white">volatile, powerful, and profitable pairs</span> in the world!
            </p>
          </div>
        </div>

        {/* Call to action */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <button
            className="px-8 py-4 rounded-full font-bold text-lg transition-all duration-300 hover:scale-105 hover:shadow-2xl"
            style={{
              backgroundColor: '#5FE785',
              color: '#05044a'
            }}
          >
            Start Learning Now
          </button>
          <button className="px-8 py-4 rounded-full font-bold text-lg border-2 border-white text-white hover:bg-white hover:text-gray-900 transition-all duration-300">
            Watch Preview
          </button>
        </div>

        {/* Stats or highlights */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16 max-w-4xl mx-auto">
          <div className="text-center">
            <div className="text-4xl font-bold mb-2" style={{ color: '#5FE785' }}>Live</div>
            <div className="text-gray-200">Market Analysis</div>
          </div>
          <div className="text-center">
            <div className="text-4xl font-bold mb-2" style={{ color: '#5FE785' }}>Real</div>
            <div className="text-gray-200">Trading Psychology</div>
          </div>
          <div className="text-center">
            <div className="text-4xl font-bold mb-2" style={{ color: '#5FE785' }}>Profitable</div>
            <div className="text-gray-200">Strategies</div>
          </div>
        </div>
      </div>

      {/* Scroll indicator */}
      {/* <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
        </svg>
      </div> */}
    </section>
  );
}
