// Import your logo image here - replace 'logo.png' with your actual logo file name
// import logo from '../assets/logo.png';

export default function Navbar() {
  return (
    <nav
      className="shadow-lg border-b-2 backdrop-blur-sm relative z-50"
      style={{
        backgroundColor: 'rgba(22, 22, 90, 0.95)',
        borderBottomColor: '#5FE785'
      }}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo and Company Name */}
          <div className="flex items-center space-x-3">
            {/* Placeholder for logo - replace src with your logo import */}
            <div
              className="h-10 w-10 rounded-lg flex items-center justify-center border-2"
              style={{
                backgroundColor: '#5FE785',
                borderColor: '#5FE785'
              }}
            >
              <span
                className="text-xs font-bold"
                style={{ color: '#05044a' }}
              >
                LOGO
              </span>
            </div>
            {/* Uncomment and use this when you add your logo image:
            <img
              src={logo}
              alt="InkChart Logo"
              className="h-10 w-10 object-contain rounded-lg"
            />
            */}
            <span
              className="text-2xl font-bold tracking-tight text-white"
            >
              InkChart
            </span>
          </div>

          {/* Optional: Add navigation items or CTA button */}
          <div className="hidden md:flex items-center space-x-6">
            <button
              className="px-6 py-2 rounded-full font-semibold text-sm transition-all duration-300 hover:scale-105 border-2"
              style={{
                backgroundColor: 'transparent',
                borderColor: '#5FE785',
                color: '#5FE785'
              }}
              onMouseEnter={(e) => {
                e.target.style.backgroundColor = '#5FE785';
                e.target.style.color = '#05044a';
              }}
              onMouseLeave={(e) => {
                e.target.style.backgroundColor = 'transparent';
                e.target.style.color = '#5FE785';
              }}
            >
              Join Webinar
            </button>
          </div>
        </div>
      </div>
    </nav>
  );
}
