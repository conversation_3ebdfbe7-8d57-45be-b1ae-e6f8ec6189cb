export default function Trendlines() {
  return (
    <section className="relative py-20 overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0" style={{ backgroundColor: '#16165A' }}>
        {/* Decorative elements */}
        <div className="absolute top-20 right-20 w-32 h-32 rounded-full opacity-5" style={{ backgroundColor: '#5FE785' }}></div>
        <div className="absolute bottom-20 left-20 w-40 h-40 rounded-full opacity-5" style={{ backgroundColor: '#05044a' }}></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

        {/* Main Title */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
            What is a Trendline?
          </h2>
        </div>

        {/* Trendline Definition Section */}
        <div className="grid lg:grid-cols-2 gap-12 items-center mb-20">
          {/* Left content */}
          <div>
            <div className="backdrop-blur-sm rounded-2xl p-8 border border-white/20" style={{ backgroundColor: 'rgb(5, 4, 74)' }}>
              <p className="text-xl text-gray-200 mb-6 leading-relaxed">
                "Market oda direction-a simple-a visualize pannura line dhaan trendline
              </p>
              <p className="text-lg text-gray-200 mb-6 leading-relaxed">
                Chart la line podra concept ellarkum theriyum.
              </p>
              <p className="text-lg text-gray-200 mb-6 leading-relaxed">
                But correct-a and meaningful-a podradhu dhaan trendline.
              </p>
              <div className="bg-white/5 rounded-xl p-6">
                <p className="text-lg text-gray-200 leading-relaxed">
                  Trendline-na, market enna side ponaa, andha direction-a namakku visually show panna help pannum."
                </p>
              </div>
            </div>
          </div>

          {/* Right content - Trade3 Image */}
          <div className="flex justify-center">
            <div className="relative">
              <img
                src="/trade3.jpeg"
                alt="Trendline Example"
                className="rounded-2xl shadow-2xl max-w-full h-auto border-2"
                style={{ borderColor: '#5FE785' }}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent rounded-2xl"></div>
            </div>
          </div>
        </div>

        {/* How to Draw Valid Trendline */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h3 className="text-3xl md:text-4xl font-bold text-white mb-4">
              How to Draw a Valid Trendline:
            </h3>
            <p className="text-xl" style={{ color: '#5FE785' }}>
              Remember this 3-rule formula:
            </p>
          </div>

          <div className="grid md:grid-cols-1 lg:grid-cols-3 gap-8 mb-12">

            {/* Rule 1 */}
            <div className="backdrop-blur-sm rounded-2xl p-8 border border-white/20 hover:border-green-400 transition-all duration-300" style={{ backgroundColor: 'rgb(5, 4, 74)' }}>
              <div className="text-center mb-6">
                <div className="w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center" style={{ backgroundColor: '#5FE785' }}>
                  <span className="text-2xl font-bold" style={{ color: '#05044a' }}>1</span>
                </div>
                <h4 className="text-2xl font-bold text-white mb-4">Rule 1: At least 3 touches</h4>
              </div>

              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                  <p className="text-gray-200">2 is not enough</p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                  <p className="text-gray-200">3+ = more confirmation</p>
                </div>
              </div>
            </div>

            {/* Rule 2 */}
            <div className="backdrop-blur-sm rounded-2xl p-8 border border-white/20 hover:border-green-400 transition-all duration-300" style={{ backgroundColor: 'rgb(5, 4, 74)' }}>
              <div className="text-center mb-6">
                <div className="w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center" style={{ backgroundColor: '#5FE785' }}>
                  <span className="text-2xl font-bold" style={{ color: '#05044a' }}>2</span>
                </div>
                <h4 className="text-2xl font-bold text-white mb-4">Rule 2: Must touch the wicks (not candle bodies)</h4>
              </div>

              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                  <p className="text-gray-200">Wick touch = price reacted</p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                  <p className="text-gray-200">Body-la touch = confusion</p>
                </div>
              </div>
            </div>

            {/* Rule 3 */}
            <div className="backdrop-blur-sm rounded-2xl p-8 border border-white/20 hover:border-green-400 transition-all duration-300" style={{ backgroundColor: 'rgb(5, 4, 74)' }}>
              <div className="text-center mb-6">
                <div className="w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center" style={{ backgroundColor: '#5FE785' }}>
                  <span className="text-2xl font-bold" style={{ color: '#05044a' }}>3</span>
                </div>
                <h4 className="text-2xl font-bold text-white mb-4">Rule 3: Should follow natural price flow</h4>
              </div>

              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                  <p className="text-gray-200">Not too steep or forced</p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                  <p className="text-gray-200">Not cutting across candles</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Why Use Trendlines Section */}
        <div className="mb-20">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left content - Trade4 Image */}
            <div className="flex justify-center">
              <div className="relative">
                <img
                  src="/trade4.jpeg"
                  alt="Trendline Usage Example"
                  className="rounded-2xl shadow-2xl max-w-full h-auto border-2"
                  style={{ borderColor: '#5FE785' }}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent rounded-2xl"></div>
              </div>
            </div>

            {/* Right content */}
            <div>
              <div className="backdrop-blur-sm rounded-2xl p-8 border border-white/20" style={{ backgroundColor: 'rgb(5, 4, 74)' }}>
                <h3 className="text-3xl font-bold mb-6 text-white">
                  Why Use Trendlines?
                </h3>

                <p className="text-lg text-gray-200 mb-6 leading-relaxed">
                  Naama chart la trendline podradhu ku oru reason irukku — not just for beauty.
                </p>

                <div className="space-y-4">
                  <div className="flex items-start space-x-4">
                    <div className="w-3 h-3 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                    <p className="text-gray-200">To find entry zones after retracement</p>
                  </div>
                  <div className="flex items-start space-x-4">
                    <div className="w-3 h-3 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                    <p className="text-gray-200">To predict possible reversals or breakouts</p>
                  </div>
                  <div className="flex items-start space-x-4">
                    <div className="w-3 h-3 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                    <p className="text-gray-200">To understand momentum and structure</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Gold Trading Psychology Section */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h3 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Gold Trading Psychology: Why 90% Lose
            </h3>
          </div>

          <div className="grid lg:grid-cols-2 gap-12">
            {/* Main Problems */}
            <div className="backdrop-blur-sm rounded-2xl p-8 border border-white/20" style={{ backgroundColor: 'rgb(5, 4, 74)' }}>
              <h4 className="text-2xl font-bold mb-6" style={{ color: '#5FE785' }}>
                Main Problems:
              </h4>

              <div className="space-y-6">
                <div className="rounded-xl p-4" style={{ backgroundColor: 'rgba(5, 4, 74, 0.5)' }}>
                  <h5 className="font-bold text-white mb-2">Overconfidence:</h5>
                  <p className="text-gray-200">Think gold always respects S/R</p>
                </div>
                <div className="rounded-xl p-4" style={{ backgroundColor: 'rgba(5, 4, 74, 0.5)' }}>
                  <h5 className="font-bold text-white mb-2">Overtrading:</h5>
                  <p className="text-gray-200">More setups = more losses</p>
                </div>
                <div className="rounded-xl p-4" style={{ backgroundColor: 'rgba(5, 4, 74, 0.5)' }}>
                  <h5 className="font-bold text-white mb-2">Revenge trading:</h5>
                  <p className="text-gray-200">Especially after fakeouts</p>
                </div>
              </div>
            </div>

            {/* Mindset Points */}
            <div className="backdrop-blur-sm rounded-2xl p-8 border border-white/20" style={{ backgroundColor: 'rgb(5, 4, 74)' }}>
              <h4 className="text-2xl font-bold mb-6" style={{ color: '#5FE785' }}>
                Mindset Points:
              </h4>

              <div className="space-y-6">
                <div className="rounded-xl p-4" style={{ backgroundColor: 'rgba(5, 4, 74, 0.5)' }}>
                  <p className="text-gray-200">Be like a sniper, not a machine gun.</p>
                </div>
                <div className="rounded-xl p-4" style={{ backgroundColor: 'rgba(5, 4, 74, 0.5)' }}>
                  <p className="text-gray-200">One good setup per week > 10 bad setups per day</p>
                </div>
                <div className="rounded-xl p-4" style={{ backgroundColor: 'rgba(5, 4, 74, 0.5)' }}>
                  <p className="text-gray-200">Accept SL, but control RR</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Final Message */}
        <div className="text-center">
          <div className="backdrop-blur-sm rounded-3xl p-8 md:p-12 border border-white/20" style={{ backgroundColor: 'rgb(5, 4, 74)' }}>
            <p className="text-xl text-gray-200 mb-6 leading-relaxed">
              Gold-la trade panradhu ore velai illa...
            </p>
            <p className="text-2xl font-bold text-white mb-8">
              Manasula patience iruntha dhaan XAU/USD oda fire-a handle panna mudiyum."
            </p>
            <button
              className="px-8 py-4 rounded-full font-bold text-lg transition-all duration-300 hover:scale-105 hover:shadow-2xl"
              style={{
                backgroundColor: '#5FE785',
                color: '#05044a'
              }}
            >
              Master Trendline Trading
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
