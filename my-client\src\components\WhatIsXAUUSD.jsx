export default function WhatIsXAUUSD() {
  return (
    <section className="relative py-20 overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0" style={{ backgroundColor: '#16165A' }}>
        {/* Decorative elements */}
        <div className="absolute top-20 left-20 w-32 h-32 rounded-full opacity-5" style={{ backgroundColor: '#5FE785' }}></div>
        <div className="absolute bottom-20 right-20 w-40 h-40 rounded-full opacity-5" style={{ backgroundColor: '#05044a' }}></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Main Title */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
            What is XAU/USD ?
          </h2>
        </div>

        {/* Introduction Section */}
        <div className="grid lg:grid-cols-2 gap-12 items-center mb-20">
          {/* Left content */}
          <div>
            <p className="text-xl text-gray-200 mb-8 leading-relaxed">
              First XAU/USD na enna, international currency exchange la
            </p>
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 mb-8">
              <p className="text-lg text-gray-200 mb-4">
                is the symbol for <span className="font-bold" style={{ color: '#5FE785' }}>Gold (XAU)</span> priced in <span className="font-bold" style={{ color: '#5FE785' }}>US Dollars (USD)</span>.
              </p>
              <div className="text-center py-6">
                <div className="text-3xl font-bold mb-2" style={{ color: '#5FE785' }}>XAU/USD = 3315</div>
                <p className="text-gray-200">It means:</p>
                <p className="text-xl font-semibold text-white">1 ounce of gold = 3315 US dollars</p>
              </div>
            </div>
          </div>

          {/* Right content - Image */}
          <div className="flex justify-center">
            <div className="relative">
              <img 
                src="/img1.jpeg" 
                alt="Gold Trading Chart" 
                className="rounded-2xl shadow-2xl max-w-full h-auto border-2"
                style={{ borderColor: '#5FE785' }}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-2xl"></div>
            </div>
          </div>
        </div>

        {/* What is 1 Ounce Section */}
        <div className="mb-20">
          <div className="bg-white/10 backdrop-blur-sm rounded-3xl p-8 md:p-12 border border-white/20">
            <h3 className="text-3xl font-bold mb-6 text-white text-center">
              What is 1 Ounce? <span className="text-lg font-normal text-gray-300">(namba normal huh pandra gram and kilo mathiri)</span>
            </h3>
            
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div>
                <p className="text-lg text-gray-200 mb-6 leading-relaxed">
                  1 ounce (specifically a troy ounce) is a unit of weight used to measure precious metals like gold, silver, platinum, etc.
                </p>
                <div className="text-center py-6 bg-white/5 rounded-xl">
                  <div className="text-2xl font-bold mb-2" style={{ color: '#5FE785' }}>1 Troy Ounce = 31.1035 grams</div>
                </div>
              </div>
              
              <div className="bg-white/5 rounded-2xl p-6">
                <p className="text-lg text-gray-200 mb-4">So when you see:</p>
                <div className="text-center">
                  <div className="text-2xl font-bold mb-2" style={{ color: '#5FE785' }}>XAU/USD = 3315</div>
                  <p className="text-gray-200">It means:</p>
                  <p className="text-xl font-semibold text-white">31.1 grams of gold = 3315 US dollars</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Gold Trust Section */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h3 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Gold isn't just traded — it's trusted.
            </h3>
            <p className="text-xl text-gray-200">
              When the world panics, it runs to gold
            </p>
            <p className="text-lg text-gray-300 mt-4">
              World la panic vantha, gold spike aagum. World la hope vantha, gold calm aagum or sell aagum.
            </p>
          </div>
        </div>

        {/* Historical Events */}
        <div className="grid md:grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* 2008 Crisis */}
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 hover:border-green-400 transition-all duration-300">
            <div className="text-center mb-6">
              <div className="w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center" style={{ backgroundColor: '#5FE785' }}>
                <span className="text-2xl font-bold" style={{ color: '#05044a' }}>08</span>
              </div>
              <h4 className="text-2xl font-bold text-white mb-2">2008 Global Financial Crisis</h4>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                <p className="text-gray-200">Lehman Brothers collapse</p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                <p className="text-gray-200">Banks failed, stocks crashed</p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                <p className="text-gray-200">People started buying gold → Gold went from <span className="font-bold text-white">$700 to over $1800</span> in the next 3 years.</p>
              </div>
            </div>
          </div>

          {/* 2020 COVID */}
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 hover:border-green-400 transition-all duration-300">
            <div className="text-center mb-6">
              <div className="w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center" style={{ backgroundColor: '#5FE785' }}>
                <span className="text-2xl font-bold" style={{ color: '#05044a' }}>20</span>
              </div>
              <h4 className="text-2xl font-bold text-white mb-2">2020 COVID Crash</h4>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                <p className="text-gray-200">March 2020: Stock market tanked, uncertainty everywhere</p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                <p className="text-gray-200">People rushed to gold → <span className="font-bold text-white">XAU/USD spiked massively</span></p>
              </div>
            </div>
          </div>

          {/* 2022 War */}
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 hover:border-green-400 transition-all duration-300">
            <div className="text-center mb-6">
              <div className="w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center" style={{ backgroundColor: '#5FE785' }}>
                <span className="text-2xl font-bold" style={{ color: '#05044a' }}>22</span>
              </div>
              <h4 className="text-2xl font-bold text-white mb-2">2022 Russia-Ukraine War</h4>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                <p className="text-gray-200">Fear of global war</p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                <p className="text-gray-200">Oil and gold both surged</p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                <p className="text-gray-200">"News la war updates varum bodhu… traders, investors, even central banks… <span className="font-bold text-white">ellarum gold-la shelter eduthu.</span>"</p>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="bg-white/5 rounded-2xl p-8 border border-white/10">
            <p className="text-xl text-gray-200 mb-6">
              Understanding these patterns is the key to successful gold trading
            </p>
            <button 
              className="px-8 py-4 rounded-full font-bold text-lg transition-all duration-300 hover:scale-105 hover:shadow-2xl"
              style={{ 
                backgroundColor: '#5FE785', 
                color: '#05044a' 
              }}
            >
              Learn Gold Trading Strategies
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
