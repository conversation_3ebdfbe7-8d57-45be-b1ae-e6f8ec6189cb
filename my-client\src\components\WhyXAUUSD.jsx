export default function WhyXAUUSD() {
  return (
    <section className="relative py-20 overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0" style={{ backgroundColor: '#16165A' }}>
        {/* Decorative elements */}
        <div className="absolute top-10 right-10 w-40 h-40 rounded-full opacity-5" style={{ backgroundColor: '#05044a' }}></div>
        <div className="absolute bottom-10 left-10 w-32 h-32 rounded-full opacity-5" style={{ backgroundColor: '#5FE785' }}></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

        {/* Why XAU/USD is Special Section */}
        <div className="mb-20">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
              WHY XAU/USD IS SPECIAL ?
            </h2>
          </div>

          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left content */}
            <div>
              <p className="text-xl text-gray-200 mb-8 leading-relaxed">
                Gold trade panradhu-na, it's not just about price going up or down.
              </p>
              <p className="text-xl text-gray-200 mb-8 leading-relaxed">
                It's about <span className="font-bold" style={{ color: '#5FE785' }}>emotions, fear, greed, and global economy.</span>
              </p>

              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="w-3 h-3 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                  <div>
                    <h4 className="font-bold text-lg mb-2 text-white">High movement = More opportunity</h4>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="w-3 h-3 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                  <div>
                    <h4 className="font-bold text-lg mb-2 text-white">Daily 200–300 pip moves</h4>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="w-3 h-3 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                  <div>
                    <h4 className="font-bold text-lg mb-2 text-white">Works well with smart money + price action strategies</h4>
                  </div>
                </div>
              </div>
            </div>

            {/* Right content */}
            <div className="bg-white rounded-3xl p-8 shadow-2xl border-2" style={{ borderColor: '#5FE785' }}>
              <div className="text-center">
                <div className="w-20 h-20 rounded-full mx-auto mb-6 flex items-center justify-center" style={{ backgroundColor: '#05044a' }}>
                  <svg className="w-10 h-10" style={{ color: '#5FE785' }} fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                </div>
                <p className="text-xl font-bold text-gray-800 leading-relaxed">
                  That's why top traders love gold — but only if you understand its game
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* What You'll Learn Today Section */}
        <div className="mb-20">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
              What You'll Learn Today ?
            </h2>
            <p className="text-xl text-gray-200 max-w-4xl mx-auto">
              Intha 1.5 hours-la, neenga learn panna porathu:
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              "What is XAU/USD — and why it's not like normal forex pairs",
              "How trendlines really work in gold",
              "Time zone strategies (Asian, London, NY moves)",
              "Smart Money vs Retail logic",
              "Custom setup: The Gold Trap strategy"
            ].map((item, index) => (
              <div key={index} className="rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border-l-4" style={{ backgroundColor: 'rgb(5, 4, 74)', borderLeftColor: '#5FE785' }}>
                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 mt-1" style={{ backgroundColor: '#05044a' }}>
                    <span className="text-white font-bold text-sm">{index + 1}</span>
                  </div>
                  <p className="text-gray-200 font-medium leading-relaxed">{item}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Target Audience Section */}
        <div className="mb-20">
          <div className="backdrop-blur-sm rounded-3xl p-8 md:p-12 shadow-2xl border border-white/20" style={{ backgroundColor: 'rgb(5, 4, 74)' }}>
            <p className="text-xl text-gray-200 mb-8 leading-relaxed">
              Itha paaka pora session, ungaluku kaagave create pannirukken.
            </p>

            <div className="grid md:grid-cols-2 gap-8">
              {[
                "Beginners — just starting with gold or forex",
                "Traders who keep losing money — but want to become consistent",
                "Side-income seekers — who want to trade part-time but smart",
                "And serious learners — who want clarity and confidence in XAU/USD"
              ].map((item, index) => (
                <div key={index} className="flex items-start space-x-4">
                  <div className="w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-1" style={{ backgroundColor: '#5FE785' }}>
                    <svg className="w-4 h-4" style={{ color: '#05044a' }} fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <p className="text-gray-200 font-medium leading-relaxed">{item}</p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Call to Action Section */}
        <div className="text-center">
          <div className="rounded-3xl p-8 md:p-12 text-white border-2" style={{ backgroundColor: '#05044a', borderColor: '#5FE785' }}>
            <h3 className="text-3xl md:text-4xl font-bold mb-8" style={{ color: '#5FE785' }}>
              Ready ah?
            </h3>

            <div className="space-y-6 max-w-4xl mx-auto">
              <div className="flex items-start space-x-4 text-left">
                <div className="w-3 h-3 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                <p className="text-lg leading-relaxed">
                  Keep notebook or mobile ready — lots of notes incoming!
                </p>
              </div>
              <div className="flex items-start space-x-4 text-left">
                <div className="w-3 h-3 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                <p className="text-lg leading-relaxed">
                  Session-kulla doubt irundha, note pannunga. Q&A end-la varum.
                </p>
              </div>
              <div className="flex items-start space-x-4 text-left">
                <div className="w-3 h-3 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                <p className="text-lg leading-relaxed">
                  Stay till the end — I'll be giving a special bonus for live viewers!
                </p>
              </div>
              <div className="flex items-start space-x-4 text-left">
                <div className="w-3 h-3 rounded-full mt-2" style={{ backgroundColor: '#5FE785' }}></div>
                <p className="text-lg leading-relaxed">
                  Trust me, this 1.5 hours might change the way you look at gold forever.
                </p>
              </div>
            </div>

            <div className="mt-10">
              <button
                className="px-10 py-4 rounded-full font-bold text-xl transition-all duration-300 hover:scale-105 hover:shadow-2xl"
                style={{
                  backgroundColor: '#5FE785',
                  color: '#05044a'
                }}
              >
                Join the Session Now
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
