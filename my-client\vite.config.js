import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import tailwindcss from '@tailwindcss/vite'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react(),
        tailwindcss()
  ],
  server: {
    host: '0.0.0.0', // Allow external connections
    port: 5173,
    strictPort: true, // Don't try other ports if 5173 is busy
    allowedHosts: [
      '.ngrok.io',
      '.ngrok-free.app',
      'localhost',
      '127.0.0.1'
    ]
  }
})
